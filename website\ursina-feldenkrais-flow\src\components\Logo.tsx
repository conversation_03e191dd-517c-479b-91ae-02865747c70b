import React from "react";

const Logo: React.FC<{ className?: string }>= ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 400 300"
    role="img"
    aria-label="Feldenkrais Urs<PERSON> – Anatomisches Becken-Logo"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>Feldenkrais U<PERSON><PERSON></title>
    <g>
      {/* Anatomical pelvis with stippling technique */}

      {/* Main pelvic outline */}
      <path d="M50 80c20-30 40-40 80-35 40-5 60 5 80 35 15 20 25 40 20 70-5 30-20 50-45 60-25 10-50 15-75 15s-50-5-75-15c-25-10-40-30-45-60-5-30 5-50 20-70z"
            stroke="currentColor" strokeWidth="1.5" fill="none"/>

      {/* Iliac crests */}
      <path d="M70 90c15-10 30-15 50-12 20-3 35 2 50 12"
            stroke="currentColor" strokeWidth="1" fill="none"/>
      <path d="M280 90c15-10 30-15 50-12 20-3 35 2 50 12"
            stroke="currentColor" strokeWidth="1" fill="none"/>

      {/* Sacrum */}
      <path d="M180 85c-8 0-15 5-18 12-3 7-2 15 2 22 4 7 10 12 18 15 8-3 14-8 18-15 4-7 5-15 2-22-3-7-10-12-18-12z"
            stroke="currentColor" strokeWidth="1" fill="none"/>

      {/* Acetabulum (hip sockets) */}
      <circle cx="120" cy="180" r="25" stroke="currentColor" strokeWidth="1.5" fill="none"/>
      <circle cx="280" cy="180" r="25" stroke="currentColor" strokeWidth="1.5" fill="none"/>

      {/* Pubic symphysis */}
      <path d="M180 220c-10 0-18 8-18 18s8 18 18 18 18-8 18-18-8-18-18-18z"
            stroke="currentColor" strokeWidth="1" fill="none"/>

      {/* Stippling dots for texture - left side */}
      <circle cx="80" cy="120" r="0.8" fill="currentColor"/>
      <circle cx="85" cy="125" r="0.6" fill="currentColor"/>
      <circle cx="90" cy="130" r="0.8" fill="currentColor"/>
      <circle cx="95" cy="135" r="0.6" fill="currentColor"/>
      <circle cx="100" cy="140" r="0.8" fill="currentColor"/>
      <circle cx="105" cy="145" r="0.6" fill="currentColor"/>
      <circle cx="110" cy="150" r="0.8" fill="currentColor"/>
      <circle cx="115" cy="155" r="0.6" fill="currentColor"/>
      <circle cx="120" cy="160" r="0.8" fill="currentColor"/>
      <circle cx="125" cy="165" r="0.6" fill="currentColor"/>
      <circle cx="130" cy="170" r="0.8" fill="currentColor"/>
      <circle cx="135" cy="175" r="0.6" fill="currentColor"/>
      <circle cx="140" cy="180" r="0.8" fill="currentColor"/>
      <circle cx="145" cy="185" r="0.6" fill="currentColor"/>
      <circle cx="150" cy="190" r="0.8" fill="currentColor"/>
      <circle cx="155" cy="195" r="0.6" fill="currentColor"/>
      <circle cx="160" cy="200" r="0.8" fill="currentColor"/>
      <circle cx="165" cy="205" r="0.6" fill="currentColor"/>

      {/* Stippling dots for texture - right side */}
      <circle cx="320" cy="120" r="0.8" fill="currentColor"/>
      <circle cx="315" cy="125" r="0.6" fill="currentColor"/>
      <circle cx="310" cy="130" r="0.8" fill="currentColor"/>
      <circle cx="305" cy="135" r="0.6" fill="currentColor"/>
      <circle cx="300" cy="140" r="0.8" fill="currentColor"/>
      <circle cx="295" cy="145" r="0.6" fill="currentColor"/>
      <circle cx="290" cy="150" r="0.8" fill="currentColor"/>
      <circle cx="285" cy="155" r="0.6" fill="currentColor"/>
      <circle cx="280" cy="160" r="0.8" fill="currentColor"/>
      <circle cx="275" cy="165" r="0.6" fill="currentColor"/>
      <circle cx="270" cy="170" r="0.8" fill="currentColor"/>
      <circle cx="265" cy="175" r="0.6" fill="currentColor"/>
      <circle cx="260" cy="180" r="0.8" fill="currentColor"/>
      <circle cx="255" cy="185" r="0.6" fill="currentColor"/>
      <circle cx="250" cy="190" r="0.8" fill="currentColor"/>
      <circle cx="245" cy="195" r="0.6" fill="currentColor"/>
      <circle cx="240" cy="200" r="0.8" fill="currentColor"/>
      <circle cx="235" cy="205" r="0.6" fill="currentColor"/>

      {/* Central stippling */}
      <circle cx="180" cy="140" r="0.6" fill="currentColor"/>
      <circle cx="185" cy="145" r="0.8" fill="currentColor"/>
      <circle cx="175" cy="145" r="0.8" fill="currentColor"/>
      <circle cx="180" cy="150" r="0.6" fill="currentColor"/>
      <circle cx="185" cy="155" r="0.8" fill="currentColor"/>
      <circle cx="175" cy="155" r="0.8" fill="currentColor"/>
      <circle cx="180" cy="160" r="0.6" fill="currentColor"/>
      <circle cx="185" cy="165" r="0.8" fill="currentColor"/>
      <circle cx="175" cy="165" r="0.8" fill="currentColor"/>
      <circle cx="180" cy="170" r="0.6" fill="currentColor"/>
      <circle cx="185" cy="175" r="0.8" fill="currentColor"/>
      <circle cx="175" cy="175" r="0.8" fill="currentColor"/>
      <circle cx="180" cy="180" r="0.6" fill="currentColor"/>
      <circle cx="185" cy="185" r="0.8" fill="currentColor"/>
      <circle cx="175" cy="185" r="0.8" fill="currentColor"/>
      <circle cx="180" cy="190" r="0.6" fill="currentColor"/>
      <circle cx="185" cy="195" r="0.8" fill="currentColor"/>
      <circle cx="175" cy="195" r="0.8" fill="currentColor"/>
    </g>
  </svg>
);

export default Logo;
