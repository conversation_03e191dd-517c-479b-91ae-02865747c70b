import React from "react";
import { useParams, Navigate } from "react-router-dom";
import { He<PERSON><PERSON>, Helmet<PERSON>rovider } from "react-helmet-async";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import Hero from "@/sections/Hero";
import Methods from "@/sections/Methods";
import About from "@/sections/About";
import Offer from "@/sections/Offer";
import Booking from "@/sections/Booking";
import Blog from "@/sections/Blog";
import Testimonials from "@/sections/Testimonials";
import Downloads from "@/sections/Downloads";
import Faq from "@/sections/Faq";
import Contact from "@/sections/Contact";
import { copy, type Lang } from "@/i18n/content";

const Index: React.FC = () => {
  const { lang } = useParams();
  const activeLang: Lang = (lang as Lang) || "de";

  if (!lang) {
    return <Navigate to="/de" replace />;
  }

  const t = copy[activeLang];
  const canonical = `https://ursinakuster.ch/${activeLang}`;

  const faqLD = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      { "@type": "Question", name: "Für wen ist Feldenkrais geeignet?", acceptedAnswer: { "@type": "Answer", text: "Für Menschen jeden Alters – besonders bei Stress, chronischen Schmerzen, nach Unfällen oder für Kinder mit besonderen Bedürfnissen." } },
      { "@type": "Question", name: "Was ist der Unterschied zwischen FI und ATM?", acceptedAnswer: { "@type": "Answer", text: "FI ist eine individuell angeleitete Einzelsitzung, ATM sind verbale Gruppenlektionen – beide fördern Wahrnehmung und leichtes Bewegen." } },
      { "@type": "Question", name: "Wie viele Sitzungen brauche ich?", acceptedAnswer: { "@type": "Answer", text: "Das ist individuell. Häufig zeigen sich nach wenigen Terminen erste Veränderungen." } },
    ],
  };

  const orgLD = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: "Feldenkrais Ursina Kuster",
    url: canonical,
    address: { "@type": "PostalAddress", addressLocality: "Zürich", addressCountry: "CH" },
    areaServed: "Zürich",
    sameAs: ["https://instagram.com", "https://facebook.com"],
    makesOffer: ["Funktionale Integration (FI)", "Awareness Through Movement (ATM)"],
  };

  const title = activeLang === 'de' ? "Feldenkrais Ursina Kuster – Zürich | FI & ATM" :
                activeLang === 'en' ? "Feldenkrais Ursina Kuster – Zurich | FI & ATM" :
                "Feldenkrais Ursina Kuster – Zurich | FI & ATM";

  const description = activeLang === 'de'
    ? "Feldenkrais in Zürich: FI & ATM. Sanft Stress abbauen, leichter bewegen, Schmerzen reduzieren. Jetzt Termin buchen."
    : activeLang === 'en'
    ? "Feldenkrais in Zurich: FI & ATM. Reduce stress, move easier, ease chronic pain. Book now."
    : "Feldenkrais à Zurich: FI & ATM. Réduire le stress, bouger plus librement. Réserver maintenant.";

  return (
    <HelmetProvider>
      <Helmet>
        <title>{title}</title>
        <meta name="description" content={description} />
        <link rel="canonical" href={canonical} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <script type="application/ld+json">{JSON.stringify(orgLD)}</script>
        <script type="application/ld+json">{JSON.stringify(faqLD)}</script>
      </Helmet>
      <Header />
      <main>
        <Hero title={t.heroTitle} subtitle={t.heroSubtitle} cta={t.cta} />
        <Methods title={t.methodTitle} points={t.methodPoints} fi={t.fi} atm={t.atm} />
        <About title={t.aboutTitle} text={t.aboutText} profileHeading={t.aboutProfileHeading} profileLead={t.aboutProfileLead} bullets={t.aboutBullets} footer={t.aboutFooter} />
        <Offer title={t.offerTitle} fi={t.fi} atm={t.atm} hint={t.offerHint} />
        <Booking title={t.bookingTitle} note={t.bookingNote} cta={t.cta} />
        <Blog />
        <Testimonials title={t.testimonialsTitle} />
        <Downloads />
        <Faq title={t.faqTitle} />
        <Contact />
        <section id="datenschutz" className="container py-8 text-sm text-muted-foreground">
          <h2 className="font-heading text-xl mb-2">Datenschutz & AGB</h2>
          <p>Diese Seite verwendet nur essentielle Cookies. Es werden keine Marketing-Cookies gesetzt.</p>
        </section>
      </main>
      <Footer />
    </HelmetProvider>
  );
};

export default Index;
