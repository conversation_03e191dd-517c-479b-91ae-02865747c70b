"use strict";
exports.oc = void 0;
var _index = require("./oc/_lib/formatDistance.js");
var _index2 = require("./oc/_lib/formatLong.js");
var _index3 = require("./oc/_lib/formatRelative.js");
var _index4 = require("./oc/_lib/localize.js");
var _index5 = require("./oc/_lib/match.js");

/**
 * @category Locales
 * @summary Occitan locale.
 * @language Occitan
 * @iso-639-2 oci
 * <AUTHOR> PAGÈS
 */
const oc = (exports.oc = {
  code: "oc",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
