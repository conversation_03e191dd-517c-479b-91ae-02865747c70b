@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL. */

@layer base {
  :root {
    /* Light mode tokens */
    --background: 0 0% 100%;
    --foreground: 222 47% 5%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    /* Brand palette */
    --brand-peach: 20 90% 66%; /* soft orange */
    --brand-blush: 345 75% 88%; /* gentle pink */
    --brand-mint: 165 55% 78%; /* complementary */

    --primary: var(--brand-peach);
    --primary-foreground: 0 0% 100%;

    --secondary: 22 80% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 42%;

    --accent: var(--brand-blush);
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 33% 92%;
    --input: 210 33% 92%;
    --ring: var(--brand-peach);

    --radius: 0.75rem;

    /* Sidebar tokens */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: var(--brand-peach);
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;

    /* Gradients & effects */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-blush)) 0%, hsl(var(--brand-peach)) 50%, hsl(var(--brand-mint)) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(0 0% 100%) 0%, hsl(210 33% 98%) 100%);
    --shadow-elegant: 0 10px 30px -10px hsl(var(--brand-peach) / 0.35);
    --shadow-glow: 0 0 40px hsl(var(--brand-peach) / 0.35);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Fonts (exposed for JS if needed) */
    --font-body: "Inter", ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
    --font-heading: "Barlow Condensed", ui-sans-serif, system-ui, sans-serif;
  }

  .dark {
    --background: 222 47% 6%;
    --foreground: 210 40% 98%;

    --card: 222 47% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 20 90% 66%;
    --primary-foreground: 222 47% 8%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 345 40% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 20 90% 66%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 20 90% 66%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * { @apply border-border; }
  html { font-family: var(--font-body); }
  body {
    @apply bg-background text-foreground antialiased;
    background-image: var(--gradient-subtle);
  }
}

@layer utilities {
  .bg-gradient-hero { background-image: var(--gradient-hero); }
  .shadow-elegant { box-shadow: var(--shadow-elegant); }
  .shadow-glow { box-shadow: var(--shadow-glow); }
  .text-gradient { background: var(--gradient-hero); -webkit-background-clip: text; background-clip: text; color: transparent; }
}
