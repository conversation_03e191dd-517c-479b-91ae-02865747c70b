import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

type Props = { title: string; fi: string; atm: string; hint: string };

const Offer: React.FC<Props> = ({ title, fi, atm, hint }) => (
  <section id="angebot" className="container py-16">
    <h2 className="font-heading text-3xl mb-8">{title}</h2>
    <div className="grid md:grid-cols-2 gap-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle>FI – Einzelsitzungen</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{fi}</p>
        </CardContent>
      </Card>
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle>ATM – Gruppenlektionen</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{atm}</p>
        </<PERSON><PERSON>ontent>
      </Card>
    </div>
    <p className="text-sm text-muted-foreground mt-4">{hint}</p>
  </section>
);

export default Offer;
