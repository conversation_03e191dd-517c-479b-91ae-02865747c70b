import React from "react";
import flowImg from "@/assets/section-flow-lines.jpg";

type Props = { title: string; points: readonly string[]; fi: string; atm: string };

const Methods: React.FC<Props> = ({ title, points, fi, atm }) => (
  <section id="methoden" className="container py-16">
    <div className="grid md:grid-cols-2 gap-10 items-center">
      <div>
        <h2 className="font-heading text-3xl mb-4">{title}</h2>
        <ul className="space-y-2 text-muted-foreground">
          {points.map((p) => (<li key={p}>• {p}</li>))}
        </ul>
        <div className="grid md:grid-cols-2 gap-6 mt-8">
          <article className="p-6 rounded-lg border bg-card shadow-elegant">
            <h3 className="font-heading text-xl mb-2">FI – Funktionale Integration</h3>
            <p className="text-sm text-muted-foreground">{fi}</p>
          </article>
          <article className="p-6 rounded-lg border bg-card shadow-elegant">
            <h3 className="font-heading text-xl mb-2">ATM – Bewusstsein durch Bewegung</h3>
            <p className="text-sm text-muted-foreground">{atm}</p>
          </article>
        </div>
      </div>
      <div>
        <img src={flowImg} alt="Fließende, weiche Linien – Lernen in Bewegung" className="rounded-xl shadow-elegant" loading="lazy" />
      </div>
    </div>
  </section>
);

export default Methods;
