import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";

const Blog: React.FC = () => {
  const posts = [
    {
      id: "feldenkrais-ueberblick",
      title: "Was ist Feldenkrais? – 3-<PERSON>uten-Überblick",
      date: "08.08.2025",
      read: "3 Min",
      excerpt:
        "Kurz erklärt, wie die Methode über Wahrnehmung das Nervensystem anspricht – mit Fokus auf Leichtigkeit statt Leistung.",
    },
    {
      id: "fi-vs-atm",
      title: "FI vs. ATM – Welche Form passt gerade zu dir?",
      date: "08.08.2025",
      read: "4 Min",
      excerpt:
        "Einzelsitzung (FI) oder Gruppenlektion (ATM)? Unterschiede, typische Anwendungsfälle und wie du startest.",
    },
    {
      id: "mini-uebung-stress",
      title: "Mini‑Übung: Stress abbauen in 2 Minuten",
      date: "08.08.2025",
      read: "2 Min",
      excerpt:
        "Eine kurze ATM‑Inspiration für zwischendurch – ruhiger atmen, Nacken entspannen und mehr Bodenkontakt spüren.",
    },
  ];

  const blogLD = {
    "@context": "https://schema.org",
    "@type": "Blog",
    blogPost: posts.map((p) => ({
      "@type": "BlogPosting",
      headline: p.title,
      datePublished: "2025-08-08",
      author: { "@type": "Person", name: "Feldenkrais Praxis" },
    })),
  };

  return (
    <section id="blog" className="container py-16">
      <h2 className="font-heading text-3xl mb-6">Blog / Artikel</h2>
      <div className="grid md:grid-cols-3 gap-6">
        {posts.map((p) => (
          <Card key={p.id} className="shadow-elegant" id={p.id}>
            <CardHeader>
              <CardTitle className="text-xl leading-snug">{p.title}</CardTitle>
              <p className="text-xs text-muted-foreground">{p.date} · {p.read} Lesezeit</p>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">{p.excerpt}</p>
              <a href="#buchung" className="text-sm underline">Jetzt Termin buchen…</a>
            </CardContent>
          </Card>
        ))}
      </div>
      <script type="application/ld+json">{JSON.stringify(blogLD)}</script>
    </section>
  );
};

export default Blog;
