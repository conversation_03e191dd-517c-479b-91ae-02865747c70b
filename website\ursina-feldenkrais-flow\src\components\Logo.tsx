import React from "react";

const Logo: React.FC<{ className?: string }>= ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 240 128"
    role="img"
    aria-label="Feldenkrais Urs<PERSON> – Anatomisches Becken-Logo"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>Feldenkrais U<PERSON><PERSON></title>
    <g>
      {/* Anatomical pelvis with stippling technique */}

      {/* Main pelvic outline */}
      <path d="M30 48c12-18 24-24 48-21 24-3 36 3 48 21 9 12 15 24 12 42-3 18-12 30-27 36-15 6-30 9-45 9s-30-3-45-9c-15-6-24-18-27-36-3-18 3-30 12-42z"
            stroke="currentColor" strokeWidth="1.2" fill="none"/>

      {/* Iliac crests */}
      <path d="M42 54c9-6 18-9 30-7 12-2 21 1 30 7"
            stroke="currentColor" strokeWidth="0.8" fill="none"/>
      <path d="M168 54c9-6 18-9 30-7 12-2 21 1 30 7"
            stroke="currentColor" strokeWidth="0.8" fill="none"/>

      {/* Sacrum */}
      <path d="M108 51c-5 0-9 3-11 7-2 4-1 9 1 13 2 4 6 7 11 9 5-2 8-5 11-9 2-4 3-9 1-13-2-4-6-7-11-7z"
            stroke="currentColor" strokeWidth="0.8" fill="none"/>

      {/* Acetabulum (hip sockets) */}
      <circle cx="72" cy="108" r="15" stroke="currentColor" strokeWidth="1.2" fill="none"/>
      <circle cx="168" cy="108" r="15" stroke="currentColor" strokeWidth="1.2" fill="none"/>

      {/* Pubic symphysis */}
      <path d="M108 132c-6 0-11 5-11 11s5 11 11 11 11-5 11-11-5-11-11-11z"
            stroke="currentColor" strokeWidth="0.8" fill="none"/>

      {/* Stippling dots for texture - left side */}
      <circle cx="48" cy="72" r="0.5" fill="currentColor"/>
      <circle cx="51" cy="75" r="0.4" fill="currentColor"/>
      <circle cx="54" cy="78" r="0.5" fill="currentColor"/>
      <circle cx="57" cy="81" r="0.4" fill="currentColor"/>
      <circle cx="60" cy="84" r="0.5" fill="currentColor"/>
      <circle cx="63" cy="87" r="0.4" fill="currentColor"/>
      <circle cx="66" cy="90" r="0.5" fill="currentColor"/>
      <circle cx="69" cy="93" r="0.4" fill="currentColor"/>
      <circle cx="72" cy="96" r="0.5" fill="currentColor"/>
      <circle cx="75" cy="99" r="0.4" fill="currentColor"/>
      <circle cx="78" cy="102" r="0.5" fill="currentColor"/>
      <circle cx="81" cy="105" r="0.4" fill="currentColor"/>
      <circle cx="84" cy="108" r="0.5" fill="currentColor"/>
      <circle cx="87" cy="111" r="0.4" fill="currentColor"/>
      <circle cx="90" cy="114" r="0.5" fill="currentColor"/>
      <circle cx="93" cy="117" r="0.4" fill="currentColor"/>
      <circle cx="96" cy="120" r="0.5" fill="currentColor"/>
      <circle cx="99" cy="123" r="0.4" fill="currentColor"/>

      {/* Stippling dots for texture - right side */}
      <circle cx="192" cy="72" r="0.5" fill="currentColor"/>
      <circle cx="189" cy="75" r="0.4" fill="currentColor"/>
      <circle cx="186" cy="78" r="0.5" fill="currentColor"/>
      <circle cx="183" cy="81" r="0.4" fill="currentColor"/>
      <circle cx="180" cy="84" r="0.5" fill="currentColor"/>
      <circle cx="177" cy="87" r="0.4" fill="currentColor"/>
      <circle cx="174" cy="90" r="0.5" fill="currentColor"/>
      <circle cx="171" cy="93" r="0.4" fill="currentColor"/>
      <circle cx="168" cy="96" r="0.5" fill="currentColor"/>
      <circle cx="165" cy="99" r="0.4" fill="currentColor"/>
      <circle cx="162" cy="102" r="0.5" fill="currentColor"/>
      <circle cx="159" cy="105" r="0.4" fill="currentColor"/>
      <circle cx="156" cy="108" r="0.5" fill="currentColor"/>
      <circle cx="153" cy="111" r="0.4" fill="currentColor"/>
      <circle cx="150" cy="114" r="0.5" fill="currentColor"/>
      <circle cx="147" cy="117" r="0.4" fill="currentColor"/>
      <circle cx="144" cy="120" r="0.5" fill="currentColor"/>
      <circle cx="141" cy="123" r="0.4" fill="currentColor"/>

      {/* Central stippling */}
      <circle cx="108" cy="84" r="0.4" fill="currentColor"/>
      <circle cx="111" cy="87" r="0.5" fill="currentColor"/>
      <circle cx="105" cy="87" r="0.5" fill="currentColor"/>
      <circle cx="108" cy="90" r="0.4" fill="currentColor"/>
      <circle cx="111" cy="93" r="0.5" fill="currentColor"/>
      <circle cx="105" cy="93" r="0.5" fill="currentColor"/>
      <circle cx="108" cy="96" r="0.4" fill="currentColor"/>
      <circle cx="111" cy="99" r="0.5" fill="currentColor"/>
      <circle cx="105" cy="99" r="0.5" fill="currentColor"/>
      <circle cx="108" cy="102" r="0.4" fill="currentColor"/>
      <circle cx="111" cy="105" r="0.5" fill="currentColor"/>
      <circle cx="105" cy="105" r="0.5" fill="currentColor"/>
      <circle cx="108" cy="108" r="0.4" fill="currentColor"/>
      <circle cx="111" cy="111" r="0.5" fill="currentColor"/>
      <circle cx="105" cy="111" r="0.5" fill="currentColor"/>
      <circle cx="108" cy="114" r="0.4" fill="currentColor"/>
      <circle cx="111" cy="117" r="0.5" fill="currentColor"/>
      <circle cx="105" cy="117" r="0.5" fill="currentColor"/>
    </g>
  </svg>
);

export default Logo;
