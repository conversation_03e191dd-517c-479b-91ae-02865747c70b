import React from "react";
import hero from "@/assets/hero-pastel-nature.jpg";
import { Button } from "@/components/ui/button";

type Props = { title: string; subtitle: string; cta: string };

const Hero: React.FC<Props> = ({ title, subtitle, cta }) => {
  return (
    <section id="start" className="relative overflow-hidden">
      <div className="absolute inset-0 -z-10">
        <img src={hero} alt="Sanfte Natur in Pastellfarben – Ruhe und Bewegung" className="h-full w-full object-cover opacity-80" loading="eager" />
        <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-background/10" />
      </div>
      <div className="container min-h-[70vh] flex flex-col items-center justify-center text-center py-16">
        <h1 className="font-heading text-4xl md:text-6xl tracking-wide mb-4 text-gradient">{title}</h1>
        <p className="max-w-2xl text-lg md:text-xl text-muted-foreground mb-8">{subtitle}</p>
        <a href="#buchung"><Button variant="hero" size="lg">{cta}</Button></a>
      </div>
    </section>
  );
};

export default Hero;
