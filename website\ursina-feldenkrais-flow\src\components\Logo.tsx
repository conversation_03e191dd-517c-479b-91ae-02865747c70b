import React from "react";

const Logo: React.FC<{ className?: string }>= ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 120 64"
    role="img"
    aria-label="Feldenkrais Urs<PERSON> – Becken-Logo"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>Feldenkrais Urs<PERSON></title>
    <g stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
      {/* Abstract pelvis outline */}
      <path d="M12 20c10-8 22-12 48-12s38 4 48 12c6 5 8 18-2 26-10 8-22 10-46 10s-36-2-46-10c-10-8-8-21-2-26z"/>
      {/* Iliac crests */}
      <path d="M26 18c6 2 10 6 12 12M94 18c-6 2-10 6-12 12"/>
      {/* Sacrum hint */}
      <path d="M60 18c0 6-2 10-6 14m12 0c-4-4-6-8-6-14"/>
    </g>
  </svg>
);

export default Logo;
