import React, { useEffect } from "react";

type Props = { title: string; note: string; cta: string };

const Booking: React.FC<Props> = ({ title, note }) => {
  // Load Appointlet inline embed script once
  useEffect(() => {
    const SCRIPT_ID = "appointlet-inline-script";
    if (document.getElementById(SCRIPT_ID)) return;
    const s = document.createElement("script");
    s.id = SCRIPT_ID;
    s.async = true;
    // Appointlet inline embed script
    s.src = "https://js.appointlet.com/";
    document.body.appendChild(s);
  }, []);

  const meetingUrl =
    "https://appt.link/feldenkrais-lektion-funktionale-integration-vUwUX261/in-person-meeting";

  return (
    <section id="buchung" className="container py-16">
      <h2 className="font-heading text-3xl mb-4">{title}</h2>
      <p className="text-muted-foreground mb-6">{note}</p>

      <div className="w-full rounded-xl border bg-card shadow-elegant p-4">
        {/* Inline Embed */}
        <div
          className="appointlet-inline min-h-[640px]"
          data-appointlet-inline={meetingUrl}
        />
        {/* Fallback link in case script is blocked */}
        <p className="text-sm text-muted-foreground mt-4">
          Falls der Kalender nicht lädt, kannst du hier direkt buchen: {" "}
          <a
            href={meetingUrl}
            className="underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            Termin buchen
          </a>
        </p>
      </div>
    </section>
  );
};

export default Booking;
