import React from "react";
import { <PERSON>, use<PERSON>ara<PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import Logo from "@/components/Logo";

const nav = [
  { href: "#methoden", label: { de: "Methode", en: "Method", fr: "Méthode" } },
  { href: "#ueber-mich", label: { de: "Über mich", en: "About", fr: "À propos" } },
  { href: "#angebot", label: { de: "Angebot", en: "Offer", fr: "Offre" } },
  { href: "#buchung", label: { de: "Buchung", en: "Booking", fr: "Réserver" } },
  { href: "#blog", label: { de: "Blog", en: "Blog", fr: "Blog" } },
  { href: "#stimmen", label: { de: "Stimmen", en: "Stories", fr: "Témoignages" } },
  { href: "#faq", label: { de: "FAQ", en: "FAQ", fr: "FAQ" } },
  { href: "#kontakt", label: { de: "Kontakt", en: "Contact", fr: "Contact" } },
];

const langs = [
  { code: "de", name: "DE" },
  { code: "en", name: "EN" },
  { code: "fr", name: "FR" },
];

const Header: React.FC = () => {
  const { lang } = useParams();
  const active = (lang as string) || "de";

  return (
    <header className="sticky top-0 z-50 backdrop-blur supports-[backdrop-filter]:bg-background/70 border-b">
      <div className="container flex items-center justify-between py-3">
        <Link to={`/${active}`} className="flex items-center gap-3">
          <Logo className="h-8 w-auto text-primary" />
          <span className="font-heading text-xl tracking-wide">Feldenkrais Ursina Kuster</span>
        </Link>
        <nav className="hidden md:flex items-center gap-6" aria-label="Hauptnavigation">
          {nav.map((n) => (
            <a key={n.href} href={n.href} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              {n.label[active as keyof typeof n.label]}
            </a>
          ))}
        </nav>
        <div className="flex items-center gap-2">
          <div className="hidden sm:flex gap-1" role="group" aria-label="Sprachen">
            {langs.map((l) => (
              <Link key={l.code} to={`/${l.code}`} className={`px-2 py-1 rounded-md border ${active===l.code?"bg-secondary text-foreground":"text-muted-foreground hover:text-foreground"}`}>
                {l.name}
              </Link>
            ))}
          </div>
          <a href="#buchung">
            <Button variant="hero" size="lg">Jetzt Termin buchen…</Button>
          </a>
        </div>
      </div>
    </header>
  );
};

export default Header;
