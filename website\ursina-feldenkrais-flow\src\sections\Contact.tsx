import React from "react";

const Contact: React.FC = () => (
  <section id="kontakt" className="container py-16">
    <h2 className="font-heading text-3xl mb-4">Kontakt & Anreise</h2>
    <div className="grid md:grid-cols-2 gap-8 items-start">
      <div className="space-y-2 text-muted-foreground">
        <p>Zürich, Schweiz</p>
        <p>E-Mail: <EMAIL></p>
        <p>ÖV & Parken: Gute An<PERSON>ng, Parkmöglichkeiten in der Nähe.</p>
        <p className="text-xs">Nur essentielle Cookies – keine Tracking-Cookies.</p>
      </div>
      <div className="rounded-xl border overflow-hidden shadow-elegant">
        <iframe
          title="Karte Zürich"
          src="https://www.google.com/maps?q=Zürich&output=embed"
          className="w-full h-[320px]"
          loading="lazy"
        />
      </div>
    </div>
  </section>
);

export default Contact;
