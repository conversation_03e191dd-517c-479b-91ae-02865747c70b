import React from "react";

type Props = { title: string; text: string; profileHeading?: string; profileLead?: string; bullets?: readonly string[]; footer?: string };

const About: React.FC<Props> = ({ title, text, profileHeading, profileLead, bullets, footer }) => (
  <section id="ueber-mich" className="container py-16">
    <div className="grid md:grid-cols-2 gap-8 items-start">
      <div className="max-w-prose">
        <h2 className="font-heading text-3xl mb-4">{title}</h2>
        <p className="text-muted-foreground leading-relaxed">{text}</p>

        {(profileHeading || profileLead || (bullets && bullets.length) || footer) && (
          <article className="mt-6 space-y-3">
            {profileHeading && <h3 className="text-xl font-medium">{profileHeading}</h3>}
            {profileLead && <p className="font-medium">{profileLead}</p>}
            {bullets && bullets.length > 0 && (
              <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                {bullets.map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            )}
            {footer && <p className="text-sm text-muted-foreground">{footer}</p>}
          </article>
        )}
      </div>
      <aside className="relative">
        <img
          src="/lovable-uploads/8b431f0b-54de-4cde-884e-cea46e7b07a9.png"
          alt="Portrait – Feldenkrais Praxis in Zürich"
          loading="lazy"
          className="w-full h-auto rounded-xl border shadow-elegant object-cover"
        />
      </aside>
    </div>
  </section>
);

export default About;
