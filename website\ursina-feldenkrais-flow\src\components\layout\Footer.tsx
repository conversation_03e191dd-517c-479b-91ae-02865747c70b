import React from "react";
import { Link, useParams } from "react-router-dom";
import { Facebook, Instagram } from "lucide-react";

const Footer: React.FC = () => {
  const { lang } = useParams();
  const active = (lang as string) || "de";
  return (
    <footer className="border-t mt-16">
      <div className="container py-10 grid gap-8 md:grid-cols-3">
        <div>
          <h3 className="font-heading text-lg mb-2">Feldenkrais Ursina Kuster</h3>
          <p className="text-sm text-muted-foreground">Zürich, Schweiz</p>
          <p className="text-sm text-muted-foreground">E-Mail: <EMAIL></p>
        </div>
        <nav className="flex flex-col gap-2" aria-label="Rechtliches">
          <a href={`/${active}#kontakt`} className="text-sm text-muted-foreground hover:text-foreground">Kontakt</a>
          <a href={`/${active}#datenschutz`} className="text-sm text-muted-foreground hover:text-foreground">Datenschutz</a>
          <a href={`/${active}#agb`} className="text-sm text-muted-foreground hover:text-foreground">AGB</a>
        </nav>
        <div className="flex items-center gap-4">
          <a href="https://instagram.com" aria-label="Instagram" className="text-muted-foreground hover:text-foreground"><Instagram /></a>
          <a href="https://facebook.com" aria-label="Facebook" className="text-muted-foreground hover:text-foreground"><Facebook /></a>
        </div>
      </div>
      <div className="py-4 text-center text-xs text-muted-foreground">© {new Date().getFullYear()} Ursina Kuster • Alle Rechte vorbehalten</div>
    </footer>
  );
};

export default Footer;
