import React from "react";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";

const items = [
  { q: "Ich bewege mich wieder frei und atme tiefer.", a: "– <PERSON>., 42" },
  { q: "<PERSON><PERSON>, humorvoll und hoch wirksam.", a: "– M.B., 58" },
  { q: "Mein Kind hat sichtbar mehr Selbstvertrauen.", a: "– <PERSON><PERSON>, Mutter" },
];

type Props = { title: string };

const Testimonials: React.FC<Props> = ({ title }) => (
  <section id="stimmen" className="container py-16">
    <h2 className="font-heading text-3xl mb-6">{title}</h2>
    <Carousel className="w-full">
      <CarouselContent>
        {items.map((it) => (
          <CarouselItem key={it.q} className="md:basis-1/2 lg:basis-1/3">
            <figure className="h-full p-6 rounded-lg border bg-card shadow-elegant flex flex-col justify-between">
              <blockquote className="text-lg">“{it.q}”</blockquote>
              <figcaption className="mt-4 text-sm text-muted-foreground">{it.a}</figcaption>
            </figure>
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  </section>
);

export default Testimonials;
