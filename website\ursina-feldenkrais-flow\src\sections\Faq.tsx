import React from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

type Props = { title: string };

const Faq: React.FC<Props> = ({ title }) => (
  <section id="faq" className="container py-16">
    <h2 className="font-heading text-3xl mb-6">{title}</h2>
    <Accordion type="single" collapsible>
      <AccordionItem value="a1">
        <AccordionTrigger>Für wen ist Feldenkrais geeignet?</AccordionTrigger>
        <AccordionContent>Für Menschen jeden Alters – besonders bei Stress, chronischen Schmerzen, nach Unfällen oder für Kinder mit besonderen Bedürfnissen.</AccordionContent>
      </AccordionItem>
      <AccordionItem value="a2">
        <AccordionTrigger>Was ist der Unterschied zwischen FI und ATM?</AccordionTrigger>
        <AccordionContent>FI ist eine individuell angeleitete Einzelsitzung, ATM sind verbale Gruppenlektionen – beide fördern Wahrnehmung und leichtes Bewegen.</AccordionContent>
      </AccordionItem>
      <AccordionItem value="a3">
        <AccordionTrigger>Wie viele Sitzungen brauche ich?</AccordionTrigger>
        <AccordionContent>Das ist individuell. Häufig zeigen sich nach wenigen Terminen erste Veränderungen.</AccordionContent>
      </AccordionItem>
    </Accordion>
  </section>
);

export default Faq;
